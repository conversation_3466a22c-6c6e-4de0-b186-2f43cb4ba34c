import { useTrans } from '@src/hooks';

interface Integration {
    id: number;
    name: string;
    description: string;
    logo: string;
    category: string;
    tags: string[];
    bgColor: string;
}

interface IntegrationCardProps {
    integration: Integration;
}

const IntegrationCard = ({ integration }: IntegrationCardProps) => {
    const t = useTrans();

    return (
        <div className=" card-shadow card-shadow   group relative flex cursor-pointer flex-col overflow-hidden rounded-4xl border border-gray-200 bg-white p-6    transition-all duration-200  hover:-translate-y-1 hover:shadow-lg lg:gap-6">
            {/* Logo */}
            <div className="mb-4">
                <div
                    className={`h-12 w-12 ${integration.bgColor} flex items-center justify-center rounded-lg text-lg font-bold text-white`}
                >
                    {integration.logo}
                </div>
            </div>

            {/* Company Name */}
            <h3 className="mb-2 text-lg font-semibold text-gray-900">
                {integration.name}
            </h3>

            {/* Description */}
            <p className="mb-4 line-clamp-3 text-sm text-gray-600">
                {integration.description}
            </p>

            {/* Tags */}
            <div className="flex flex-wrap gap-2">
                {integration.tags.map((tag, index) => (
                    <span
                        key={index}
                        className="inline-flex items-center rounded-full bg-blue-100 px-2.5 py-0.5 text-xs font-medium text-blue-800"
                    >
                        {tag}
                    </span>
                ))}
            </div>
        </div>
    );
};

export default IntegrationCard;
